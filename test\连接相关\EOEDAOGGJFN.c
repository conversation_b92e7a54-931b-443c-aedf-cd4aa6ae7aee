__int64 __fastcall SFSController__EOEDAOGGJFN(_QWORD *Params, __int64 a2)
{
  _QWORD *Params_1; // x19
  __int64 v4; // x8
  _QWORD *Params_2; // x20
  __int64 v6; // x9
  __int64 StringLiteral_19257; // x21
  int *v8; // x10
  __int64 v9; // x0
  __int64 v10; // x0
  __int64 result; // x0
  __int64 v12; // x8
  __int64 v13; // x8
  __int64 v14; // x8
  __int64 v15; // x0

  Params_1 = Params;
  if ( (byte_490BBE6 & 1) == 0 )
  {
    sub_184E408(&bool_TypeInfo);
    sub_184E408(&GameController_TypeInfo);
    sub_184E408(&System_Collections_Generic_IDictionary_string__object__TypeInfo);
    sub_184E408(&StringLiteral_19257);
    sub_184E408(&StringLiteral_4787);
    Params = (_QWORD *)sub_184E408(&StringLiteral_4772);
    byte_490BBE6 = 1;
  }
  if ( !a2 )
    goto LABEL_37;
  Params = (_QWORD *)Sfs2X_Core_BaseEvent__get_Params(a2, 0LL);
  if ( !Params )
    goto LABEL_37;
  v4 = *Params;
  Params_2 = Params;
  v6 = *(unsigned __int16 *)(*Params + 302LL);
  StringLiteral_19257 = StringLiteral_19257;
  if ( *(_WORD *)(*Params + 302LL) )
  {
    v8 = (int *)(*(_QWORD *)(v4 + 176) + 8LL);
    while ( *((_QWORD *)v8 - 1) != System_Collections_Generic_IDictionary_string__object__TypeInfo )
    {
      --v6;
      v8 += 4;
      if ( !v6 )
        goto LABEL_9;
    }
    v9 = v4 + 16LL * *v8 + 312;
  }
  else
  {
LABEL_9:
    v9 = sub_185BD18(Params, System_Collections_Generic_IDictionary_string__object__TypeInfo, 0LL);
  }
  Params = (_QWORD *)(*(__int64 (__fastcall **)(_QWORD *, __int64, _QWORD))v9)(
                       Params_2,
                       StringLiteral_19257,
                       *(_QWORD *)(v9 + 8));
  if ( !Params )
    goto LABEL_37;
  if ( *(_QWORD *)(*Params + 64LL) == *((_QWORD *)bool_TypeInfo + 8) )
  {
    if ( *(_BYTE *)j_il2cpp_object_unbox_0() )
    {
      if ( *((_BYTE *)Params_1 + 96) )
      {
        v10 = Params_1[8];
        if ( v10 )
          return Sfs2X_SmartFox__InitCrypto(v10, 0LL);
      }
      SFSController__HCAJEPAGHLL(Params_1);
      result = CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
                 Params_1[94],
                 *((unsigned int *)Params_1 + 190),
                 0LL);
      if ( (result & 1) != 0 )
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        Params = (_QWORD *)GameController__get_instance(0LL);
        if ( Params )
        {
          v12 = Params[12];
          if ( v12 )
          {
            Params = *(_QWORD **)(v12 + 160);
            if ( Params )
              return UI_Message__Show(Params, StringLiteral_4772, 1LL, 0LL);
          }
        }
LABEL_37:
        sub_184E634(Params);
      }
    }
    else
    {
      if ( (CodeStage_AntiCheat_ObscuredTypes_ObscuredBool__op_Implicit_26302352(
              Params_1[94],
              *((unsigned int *)Params_1 + 190),
              0LL) & 1) != 0 )
      {
        if ( !*((_DWORD *)GameController_TypeInfo + 56) )
          j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
        Params = (_QWORD *)GameController__get_instance(0LL);
        if ( !Params )
          goto LABEL_37;
        v13 = Params[12];
        if ( !v13 )
          goto LABEL_37;
        Params = *(_QWORD **)(v13 + 160);
        if ( !Params )
          goto LABEL_37;
        UI_Message__Show(Params, StringLiteral_4787, 1LL, 0LL);
      }
      SFSController__DNIBMLBAGDM(Params_1, 1LL);
      if ( !*((_DWORD *)GameController_TypeInfo + 56) )
        j_il2cpp_runtime_class_init_0(GameController_TypeInfo);
      Params = (_QWORD *)GameController__get_instance(0LL);
      if ( !Params )
        goto LABEL_37;
      Params = (_QWORD *)Params[15];
      if ( !Params )
        goto LABEL_37;
      result = NetworkManager__set_ErrorConnectingSFS(Params, 1LL, 0LL);
      v14 = Params_1[15];
      if ( v14 )
        return (*(__int64 (__fastcall **)(_QWORD, _QWORD))(v14 + 24))(*(_QWORD *)(v14 + 64), *(_QWORD *)(v14 + 40));
    }
  }
  else
  {
    v15 = sub_184E9B4();
    return SFSController__DIDFCDEJNAI(v15);
  }
  return result;
}